import { itemUOM, ProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import {
  createProductionPlan,
  ProductionPlanDto,
  updateProductionPlan,
  updateProductionPlanStatus,
} from '@/services/seasonal-management/productionPlan';
import { Environment } from '@/services/vietplants/environment';
import { getFormPlantList } from '@/services/vietplants/plant';
import {
  ModalForm,
  ProColumns,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Col, Form, message, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useAllocationData, useAllocationEventListener } from '../../../hooks/useAllocationData';
import { useGetEnvList } from '../../../hooks/useGetEnvList';
import { useGetItemList } from '../../../hooks/useGetItemList';
import '../../../styles/EditableTableOverride.less';
import { EnhancedEditableTable } from '../../AllocationDisplay/EnhancedEditableTable';
import { UpdateProductionPlanFormProps, WeekColumn, YieldTableRow } from '../types';
import { generateNewRowId, getWeeks } from '../utils';

export const UpdateProductionPlanForm: FC<UpdateProductionPlanFormProps> = ({
  open,
  onOpenChange,
  onSuccess,
  initialData,
  reloadYieldTableAndExpectedOutputTable,
  setReloadYieldTableAndExpectedOutputTable,
}) => {
  const [form] = Form.useForm();
  const { formatMessage } = useIntl();
  const [weeks, setWeeks] = useState<WeekColumn[]>([]);
  const [yieldData, setYieldData] = useState<YieldTableRow[]>([]);
  const [environmentOptions, setEnvironmentOptions] = useState<{ value: string; label: string }[]>(
    [],
  );
  const [itemOptions, setItemOptions] = useState<
    { value: string; label: string; uoms: itemUOM[]; environment_id: string }[]
  >([]);
  const [isAssigning, setIsAssigning] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Check if plan is assigned
  const isPlanAssigned = initialData?.status === 'assigned';

  // Allocation data management
  const {
    data: allocationData,
    loading: allocationLoading,
    refresh: refreshAllocationData,
  } = useAllocationData({
    planId: initialData?.name,
    autoRefresh: isPlanAssigned,
    refreshInterval: 30000, // 30 seconds
  });

  // Listen for allocation events
  useAllocationEventListener(() => {
    if (isPlanAssigned) {
      refreshAllocationData();
    }
  });

  const { run: getItemList } = useGetItemList({
    onSuccess: useCallback(
      (data: ProductItemV3[]) =>
        setItemOptions(
          data.map((i) => ({
            value: i.name,
            label: i.item_name,
            uoms: i.uoms,
            environment_id: i.environment_id || '',
          })),
        ),
      [],
    ),
    onError: useCallback(() => setItemOptions([]), []),
  });

  const { run: getEnvList } = useGetEnvList({
    onSuccess: useCallback((data: Environment[]) => {
      const activeEnvironments = data.filter((env) => env.is_disabled === false);
      const options = activeEnvironments.map((env) => ({
        value: env.name,
        label: env.label,
      }));
      setEnvironmentOptions(options);
    }, []),
    onError: useCallback(() => {
      setEnvironmentOptions([]);
    }, []),
  });

  const handleDateChange = useCallback(
    (dates: [Dayjs | null, Dayjs | null], dateStrings: [string, string]) => {
      const [from, to] = dateStrings;
      const start = dayjs(from);
      const end = dayjs(to);
      const newWeeks = getWeeks(start, end);
      setWeeks(newWeeks);
      setYieldData([]);
      setEnvironmentOptions([]);
    },
    [],
  );

  // Helper function to check if a week is in the future
  const isWeekInFuture = useCallback((weekDataIndex: string) => {
    const [startDateStr] = weekDataIndex.replace('week_', '').split('_');
    const weekStartDate = dayjs(startDateStr);
    const today = dayjs().startOf('day');
    return weekStartDate.isAfter(today) || weekStartDate.isSame(today);
  }, []);

  const yieldColumns = useMemo<ProColumns<YieldTableRow>[]>(
    () => [
      {
        width: 200,
        title: formatMessage({ id: 'common.environment' }),
        dataIndex: 'environment_template_id',
        valueType: 'select' as const,
        fieldProps: {
          options: environmentOptions,
          showSearch: true,
          filterOption: (input: string, option?: { label: string; value: string }) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
        },
        // Add render function to display environment label in the table
        render: (_text, record) => {
          const environment = environmentOptions.find(
            (env) => env.value === record.environment_template_id,
          );
          return environment?.label || record.environment_template_id;
        },
        editable: (text: any, record: YieldTableRow, index: number) => true,
        key: 'environment_template_id',
        formItemProps: {
          rules: [
            {
              required: true,
              message: formatMessage({ id: 'common.plan_creation.environment_is_required' }),
            },
          ],
        },
      },
      {
        width: 200,
        title: 'Mẫu giống',
        dataIndex: 'item_id',
        valueType: 'select' as const,
        renderFormItem(schema, config, form, action) {
          const currentRecord = config.record ? config.record : yieldData[0];
          const selectedEnvironmentId = currentRecord?.environment_template_id;

          // Filter items by environment_id matching the selected environment's name
          const filteredItems = itemOptions.filter(
            (item) => item.environment_id === selectedEnvironmentId,
          );

          return (
            <ProFormSelect
              style={{ width: '100%', marginBottom: 0 }}
              options={filteredItems.map((item) => ({
                label: item.label,
                value: item.value,
              }))}
              fieldProps={{
                showSearch: true,
                filterOption: (input: string, option: any) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
                placeholder: selectedEnvironmentId
                  ? 'Chọn item'
                  : 'Vui lòng chọn environment trước',
                disabled: !selectedEnvironmentId,
              }}
            />
          );
        },
        // Render function to display item label in the table
        render: (_text, record) => {
          const item = itemOptions.find((i) => i.value === record.item_id);
          return item?.label || record.item_id;
        },
        editable: (text: any, record: YieldTableRow, index: number) => true,
        key: 'item_id',
        formItemProps: {
          rules: [
            {
              required: true,
              message: formatMessage({ id: 'common.plan_creation.item_is_required' }),
            },
          ],
        },
      },
      {
        width: 200,
        title: formatMessage({ id: 'common.uom' }),
        dataIndex: 'uom_id',
        valueType: 'select' as const,
        // fieldProps(form, config) {
        //   console.log('config', config);
        //   console.log('form', form);
        //   // const item = itemOptions.find((i) => i.value === config.record.item_id);
        //   return {
        //     options: [],
        //     showSearch: true,
        //     filterOption: (input: string, option?: { label: string; value: string }) =>
        //       (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
        //   };
        // },
        renderFormItem(schema, config, form, action) {
          const currentRecord = config.record ? config.record : yieldData[0];
          const item = itemOptions.find((i) => i.value === currentRecord.item_id);
          return (
            <ProFormSelect
              style={{ width: '100%', marginBottom: 0 }}
              options={item?.uoms.map((uom) => ({ label: uom.uom_name, value: uom.uom })) || []}
              showSearch
              initialValue={item?.uoms[0]?.uom}
            />
          );
        },
        // thêm render này để khi table render cell sẽ dùng label
        render: (_text, record) => {
          const item = itemOptions.find((i) => i.value === record.item_id);
          const uom = item?.uoms.find((u) => u.uom === record.uom_id);
          return uom?.uom_name || record.uom_id;
        },
        editable: (text: any, record: YieldTableRow, index: number) => true,
        key: 'uom_id',
        formItemProps: {
          rules: [
            {
              required: true,
              // message: formatMessage({ id: 'common.plan_creation.uom_is_required' }),
            },
          ],
        },
      },
      ...weeks.map((w) => ({
        width: 100,
        title: w.title,
        dataIndex: w.dataIndex,
        valueType: 'digit' as const,
        editable: (text: any, record: YieldTableRow, index: number) => {
          // If plan is not assigned, allow all edits
          if (!isPlanAssigned) return true;
          // If plan is assigned, only allow editing future weeks
          return isWeekInFuture(w.dataIndex);
        },
        key: w.key,
      })),
    ],
    [weeks, formatMessage, environmentOptions, itemOptions, isPlanAssigned, isWeekInFuture],
  );

  useEffect(() => {
    if (open && !environmentOptions.length) {
      getEnvList();
    }

    if (!open) {
      form.resetFields();
      setWeeks([]);
      setYieldData([]);
      setEnvironmentOptions([]);
      setItemOptions([]);
    }
  }, [open, environmentOptions.length, form, getEnvList]);

  useEffect(() => {
    if (open && !itemOptions.length) {
      getItemList();
    }
  }, [open, itemOptions.length, getItemList]);

  useEffect(() => {
    if (initialData && open) {
      form.setFieldsValue({
        label: initialData.label,
        plant_id: initialData.plant_id,
        application_date:
          initialData.start_date || initialData.end_date
            ? [
                initialData.start_date ? dayjs(initialData.start_date) : undefined,
                initialData.end_date ? dayjs(initialData.end_date) : undefined,
              ]
            : undefined,
        notes: initialData.notes,
      });

      // Set weeks based on initial data
      const start = dayjs(initialData.start_date);
      const end = dayjs(initialData.end_date);
      const newWeeks = getWeeks(start, end);
      setWeeks(newWeeks);

      // Transform yield data - Group by environment_template_id + item_id combination
      const envItemGroups = (initialData.yieldExpectedOutputs || []).reduce(
        (acc: Record<string, any[]>, item: any) => {
          // Create composite key from environment_template_id and item_id
          const compositeKey = `${item.environment_template_id}_${item.item_id || ''}`;
          if (!acc[compositeKey]) acc[compositeKey] = [];
          acc[compositeKey].push(item);
          return acc;
        },
        {},
      );

      const transformedYieldData = Object.entries(envItemGroups).map(([compositeKey, items]) => {
        const envItems = items as any[];
        const row: YieldTableRow = {
          id: compositeKey, // Use composite key as unique ID
          environment_template_id: envItems[0].environment_template_id,
          name: envItems[0].name,
          yield_value: 0,
          item_id: envItems[0].item_id || '',
          uom_id: envItems[0].uom_id || '',
          // Add allocation data if available (fallback values)
          allocated_quantity: envItems[0].allocated_quantity || 0,
          remaining_quantity: envItems[0].remaining_quantity || 0,
          weekAllocationData: {},
        };

        newWeeks.forEach((week) => {
          const [weekStart, weekEnd] = week.dataIndex.replace('week_', '').split('_');

          // Find yield output that overlaps with this week
          const found = envItems.find((i: any) => {
            const yieldStart = dayjs(i.start_date);
            const yieldEnd = dayjs(i.end_date);
            const weekStartDate = dayjs(weekStart);
            const weekEndDate = dayjs(weekEnd);

            // Check if there's any overlap between yield period and week period
            return (
              (yieldStart.isBefore(weekEndDate) || yieldStart.isSame(weekEndDate)) &&
              (yieldEnd.isAfter(weekStartDate) || yieldEnd.isSame(weekStartDate))
            );
          });

          row[week.dataIndex] = found ? found.yield_value : 0;
          if (found && row.yield_value === 0) row.yield_value = found.yield_value;

          // Store allocation data for this specific week
          if (found) {
            row.weekAllocationData![week.dataIndex] = {
              allocated_quantity: found.allocated_quantity || 0,
              remaining_quantity: found.remaining_quantity || 0,
              expected_output: found.expected_output || 0,
            };
          }
        });
        return row;
      });
      setYieldData(transformedYieldData);
    }
  }, [initialData, open, form]);

  useEffect(() => {
    if (!open) {
      setIsAssigning(false);
    }
  }, [open]);

  const handleFinish = useCallback(
    async (values: any) => {
      setIsSaving(true);

      const planDto: ProductionPlanDto = {
        name: initialData?.name,
        label: values.label || initialData?.label,
        plant_id: values.plant_id,
        start_date: values.application_date[0],
        end_date: values.application_date[1],
        notes: values.notes,
        status: initialData?.status || 'plan',
        yieldExpectedOutputs: yieldData.flatMap((row) => {
          const envId = row.environment_template_id;

          if (!envId) return []; // Skip if no environment selected

          // Ensure item_id and uom_id are set
          const item_id = row.item_id || '';
          const uom_id = row.uom_id || '';

          return Object.entries(row)
            .filter(([key]) => key.startsWith('week_'))
            .map(([weekKey, yieldValue]) => {
              const [startDateStr, endDateStr] = weekKey.replace('week_', '').split('_');

              // Find existing yield output to preserve the name - match by environment_template_id, item_id, and date range
              const existingYieldOutput = (initialData?.yieldExpectedOutputs || []).find(
                (existing: any) =>
                  existing.environment_template_id === envId &&
                  existing.item_id === item_id &&
                  existing.start_date === startDateStr &&
                  existing.end_date === endDateStr,
              );

              return {
                name: existingYieldOutput?.name, // Preserve existing name for updates
                environment_template_id: envId,
                start_date: startDateStr,
                end_date: endDateStr,
                yield_value: Number(yieldValue) || 0,
                expected_output: existingYieldOutput?.expected_output || 0,
                item_id,
                uom_id,
              };
            });
        }),
        expectedMaterials: [],
      };

      try {
        if (initialData?.name) {
          // Update existing plan
          await updateProductionPlan(initialData.name, planDto);
        } else {
          // Create new plan (fallback)
          await createProductionPlan(planDto);
        }
        message.success('Cập nhật kế hoạch thành công!');
        onSuccess();
        return false;
      } catch (err) {
        console.error('Error updating production plan:', err);
        message.error('Cập nhật kế hoạch thất bại!');
        return false;
      } finally {
        setIsSaving(false);
      }
    },
    [yieldData, initialData?.name, onSuccess],
  );

  const handleAssign = async () => {
    if (!initialData?.name) return;
    setIsAssigning(true);
    try {
      await updateProductionPlanStatus(initialData.name, { status: 'assigned' });
      message.success('Giao việc thành công!');
      onSuccess();
      onOpenChange(false);
    } catch (err) {
      message.error('Giao việc thất bại!');
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <ModalForm
      form={form}
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{formatMessage({ id: 'common.update_production_plan' })}</span>
          {/* {isPlanAssigned && (
            <Tooltip title="Làm mới dữ liệu phân bổ">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                loading={allocationLoading}
                onClick={refreshAllocationData}
                size="small"
              />
            </Tooltip>
          )} */}
        </div>
      }
      width={1600}
      open={open}
      onOpenChange={(visible) => {
        // if (!visible) {
        //   // Gọi onSuccess để reload table khi đóng modal
        //   onSuccess();
        // }
        onOpenChange(visible);
      }}
      onFinish={handleFinish}
      submitter={{
        render: (props) => [
          <Button
            key="close"
            onClick={() => {
              onOpenChange(false);
              setIsAssigning(false);
            }}
            style={{ marginRight: 8 }}
          >
            Đóng
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => props.form?.submit?.()}
            loading={isSaving}
          >
            Lưu
          </Button>,
          <Button
            key="assign"
            type="default"
            onClick={handleAssign}
            disabled={isPlanAssigned}
            loading={isAssigning}
            style={{ marginLeft: 8 }}
          >
            {isPlanAssigned ? 'Đã giao việc' : 'Giao việc'}
          </Button>,
        ],
      }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <ProFormText
            name="label"
            label={formatMessage({ id: 'common.plan_name' })}
            rules={[
              { required: true },
              {
                max: 100,
                message: formatMessage({
                  id: 'common.form-management.department.plan_name_too_long',
                }),
              },
            ]}
            disabled={isPlanAssigned}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="plant_id"
            label={formatMessage({ id: 'common.select_plant' })}
            rules={[{ required: true }]}
            request={async () => {
              const plantList = await getFormPlantList();
              return plantList.data.map((plant) => ({
                value: plant.name,
                label: plant.label,
              }));
            }}
            showSearch
            disabled={isPlanAssigned}
          />
        </Col>
      </Row>

      <Row>
        <Col span={24}>
          <ProFormDateRangePicker
            name="application_date"
            label={formatMessage({ id: 'common.application_date' })}
            rules={[{ required: true }]}
            fieldProps={{ onChange: handleDateChange }}
            style={{ width: '100%' }}
            disabled={isPlanAssigned}
          />
        </Col>
      </Row>

      <Row>
        <Col span={24}>
          <div className="my-editable-table">
            {' '}
            <EnhancedEditableTable
              data={yieldData}
              setData={(newData: YieldTableRow[]) => {
                // Make sure we explicitly set the item_id and uom_id for all rows
                // and update the composite key when environment or item changes
                const processedData = newData.map((row) => {
                  const updatedRow = {
                    ...row,
                    item_id: row.item_id || '',
                    uom_id: row.uom_id || '',
                  };

                  // Update composite key if both environment and item are selected
                  if (updatedRow.environment_template_id && updatedRow.item_id) {
                    updatedRow.id = `${updatedRow.environment_template_id}_${updatedRow.item_id}`;
                  }

                  return updatedRow;
                });

                // Check for duplicate combinations and show warning
                const duplicateGroups = processedData.reduce(
                  (acc: Record<string, number>, row, index) => {
                    if (row.environment_template_id && row.item_id) {
                      const key = `${row.environment_template_id}_${row.item_id}`;
                      acc[key] = (acc[key] || 0) + 1;
                    }
                    return acc;
                  },
                  {},
                );

                const hasDuplicates = Object.values(duplicateGroups).some((count) => count > 1);
                if (hasDuplicates) {
                  message.warning(
                    'Phát hiện combination trùng lặp của Environment và Item. Vui lòng kiểm tra lại!',
                  );
                }

                setYieldData(processedData);
              }}
              columns={yieldColumns}
              weeks={weeks}
              title="common.yield_table"
              onCreateRow={() => {
                const newRowId = generateNewRowId();
                return {
                  id: newRowId, // Will be updated to composite key when environment and item are selected
                  name: newRowId, // Add name field for consistency
                  environment_template_id: '',
                  yield_value: 0,
                  item_id: '',
                  uom_id: '',
                  allocated_quantity: 0,
                  remaining_quantity: 0,
                  weekAllocationData: {},
                };
              }}
              disableAdd={isPlanAssigned}
              showAllocationData={isPlanAssigned}
              isAssigned={isPlanAssigned}
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Col span={24}>
          <ProFormTextArea name="notes" label={formatMessage({ id: 'common.note' })} />
        </Col>
      </Row>
    </ModalForm>
  );
};

/* eslint-disable no-useless-catch */
import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { IFarmPlanRes } from '@/types/IFarmPlanRes.type';
import { IQualityControl } from '@/types/IQualityControl';
import { request } from '@umijs/max';
import { generateAPIPath, getParamsReqList } from './utils';
import { Environment } from './vietplants/environment';

export const getFarmingPlanList = async (params?: API.ListParamsReq) => {
  const res = await request(generateAPIPath('api/v2/farmingPlan/plan'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return (res as API.PaginationResponseResult<IFarmPlanRes[]>).result;
};

export const getFarmingPlanFromTemplateCropList = async (params?: API.ListParamsReq) => {
  const res = await request(generateAPIPath('api/v2/farmingPlan/plan-from-template-crop'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return (res as API.PaginationResponseResult<IFarmPlanRes[]>).result;
};

export const getFarmingPlan = async (planId?: string, filters: any = [[]]) => {
  let baseFilter: any = [];
  if (planId !== '') {
    baseFilter = [[`${DOCTYPE_ERP.iotFarmingPlan}`, 'name', 'like', `${planId}`]];
  }
  // Combine the base filter with any additional filters
  const allFilters = [...baseFilter];
  allFilters.push(...filters);
  console.log('allFilters', allFilters);
  try {
    console.log('start to get plan');
    const response: any = await request<API.ResponseResult<{ data: IFarmPlanRes[] }>>(
      generateAPIPath('api/v2/farmingPlan/plan'),
      {
        method: 'GET',
        params: {
          filters: JSON.stringify(allFilters),
        },
      },
    );
    console.log('farmPlanData', response.result);
    // Assuming result always has data, but you might want to add additional checks
    const farmPlanData: any = response.result.data.length ? response.result.data[0] : {};
    return { data: farmPlanData };
  } catch (error) {
    // Handle errors appropriately, e.g., logging or rethrowing
    console.error('Error fetching farming plan:', error);
    throw error;
  }
};

export const createFarmingPlan = async (data: {
  // modified_by: string;
  // owner: string;
  // docstatus: number;
  // idx: number;
  crop: string;
  // grow_time: any;
  // _user_tags: any;
  // _comments: any;
  // _assign: any;
  // _liked_by: any;
  label: string;
  image?: any;
  start_date: string;
  end_date: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: IFarmPlanRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/plan'), {
    method: 'POST',
    data,
  });
  return res.result;
};

export const createFarmingPlanFromCopy = async (data: {
  // modified_by: string;
  // owner: string;
  // docstatus: number;
  // idx: number;
  crop: string;
  // grow_time: any;
  // _user_tags: any;
  // _comments: any;
  // _assign: any;
  // _liked_by: any;
  label: string;
  image?: any;
  start_date: string;
  end_date: string;
  copy_plan_id: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: IFarmPlanRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/planFromCopy'), {
    method: 'POST',
    data,
  });
  return res.result;
};

export interface IUpdateFarmingPlanReq {
  name: string;
  owner?: string;
  creation?: string;
  modified?: string;
  modified_by?: string;
  docstatus?: number;
  idx?: number;
  crop?: string;
  label?: string;
  image?: any;
  start_date?: string;
  end_date?: string;
  crop_name?: string;
  is_deleted?: number;
  // doctype: string;
}

export const updateFarmingPlan = async (data: IUpdateFarmingPlanReq) => {
  const res = await request<
    API.ResponseResult<{
      data: IFarmPlanRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/plan'), {
    method: 'PUT',
    data,
  });
  return res.result;
};
export const deletePlan = async ({ name }: { name: string }) => {
  const res = await request<
    API.ResponseResult<{
      data: ICreateFarmingPlanStateRes;
    }>
  >(generateAPIPath(`api/v2/farmingPlan/plan?name=${name}`), {
    method: 'DELETE',
  });
  return res.result;
};
export type IFarmingPlanStateRes = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  label: string;
  image: any;
  start_date: string;
  end_date: string;
  index: any;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  farming_plan: string;
  crop_name: string;
};
export const getFarmingPlanState = async (params?: API.ListParamsReq) => {
  try {
    const res = await request<
      API.ResponseResult<{
        data: IFarmingPlanStateRes[];
      }>
    >(generateAPIPath('api/v2/farmingPlan/state'), {
      method: 'GET',
      params: getParamsReqList(params),
    });
    return res.result;
  } catch (error) {
    throw error;
  }
};

export interface ICreateFarmingPlanStateRes {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  farming_plan: string;
  crop_name: string;
  label: string;
  image: any;
  start_date: string;
  end_date: string;
  index: any;
  doctype: string;
}

export const createFarmingPlanState = async (data: {
  label: string;
  farming_plan: string;
  start_date: string;
  end_date: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: ICreateFarmingPlanStateRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/state'), {
    method: 'POST',
    data,
  });
  return res.result;
};
export type IUpdatePlanStateReq = {
  name: string;
  label?: string;
  farming_plan: string;
  start_date?: string;
  end_date?: string;
};
export type IUpdatePlanStateRes = {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  farming_plan: string;
  crop_name: string;
  label: string;
  start_date: string;
  end_date: string;
  doctype: string;
};
export const updatePlanState = async (data: IUpdatePlanStateReq) => {
  const res = await request<
    API.ResponseResult<{
      data: IUpdatePlanStateRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/state'), {
    method: 'PUT',
    data,
  });
  return res.result;
};
export const deletePlanState = async ({ name }: { name: string }) => {
  const res = await request<
    API.ResponseResult<{
      exc_type: 'DoesNotExistError';
      message: 'ok';
    }>
  >(generateAPIPath(`api/v2/farmingPlan/state?name=${name}`), {
    method: 'DELETE',
  });
  return res.result;
};

export interface ITaskManagerRes {
  name: string;
  label: string;
  assigned_to: string;
  assigned_to_info: AssignedToInfo[];
  involved_in: string;
  start_date: string;
  end_date: string;
  status: string;
  pl_state_id: string;
  pl_state_name: string;
  pl_id: string;
  pl_name: string;
  crop_id: string;
  crop_name: string;
  zone_id: string;
  zone_name: string;
  project_id: string;
  project_name: string;
  customer_id: string;
  todo_total: string;
  todo_done: string;
  tag_color: string | null;
  tag_label: string | null;
  description: string;
  todo_list: ITaskTodo[];
  department_id?: string;
  environment_template_id?: string;
  involved_in_info: InvolvedInInfo[];
  involve_in_users: InvolvedInInfo[];
  item_list: any;
  prod_quantity_list: any;

  quality?: IQualityControl[];
  environmentTemplate?: Environment;
}
export interface IDiaryTask {
  name: string;
  label: string;
  assigned_to: string;
  assigned_to_info: AssignedToInfo[];
  involved_in: string;
  start_date: string;
  end_date: string;
  status: string;
  crop_id: string;
  crop_name: string;
  zone_id: string;
  zone_name: string;
  project_id: string;
  project_name: string;
  customer_id: string;
  todo_total: string;
  todo_done: string;
  tag_color: string | null;
  tag_label: string | null;
  description: string;
  todo_list: ITaskTodo[];
  department_id?: string;
  environment_template_id?: string;
  involved_in_info: InvolvedInInfo[];
  involve_in_users: InvolvedInInfo[];
  state_name: string;
  plan_name: string;
  item_list?: any[];
  prod_quantity_list?: any[];
  text_assign_user: string;
  text_state: string;
  text_plan: string;
}
export interface ITaskTodo {
  label: string;
  status: string;
  description: string;
}

export interface AssignedToInfo {
  user_id: string;
  first_name: string;
  last_name: string;
  user_avatar: any;
}

export interface InvolvedInInfo {
  user_id: string;
  first_name: string;
  last_name: string;
  user_avatar: any;
}

export const getDiaryTaskList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: IDiaryTask[];
    }>
  >(generateAPIPath('api/v2/farmingPlan/task-management-info/diary'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getTaskManagerList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ITaskManagerRes[];
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
      };
    }>
  >(generateAPIPath('api/v2/farmingPlan/task-management-info'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};
interface IgetTaskManagerListV2 {
  productionPlanId?: string;
}

export const getTaskManagerListV2 = async (params?: API.ListParamsReq & IgetTaskManagerListV2) => {
  const res = await request<
    API.ResponseResult<{
      data: ITaskManagerRes[];
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
      };
    }>
  >(generateAPIPath('api/v2/seasonal-management/farming-plan-task/task-management-info'), {
    method: 'GET',
    params,
  });
  return res.result;
};

export const getTemplateTaskManagerList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ITaskManagerRes[];
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
      };
    }>
  >(generateAPIPath('api/v2/farmingPlan/task-management-info/template'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getAllTaskManagerList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ITaskManagerRes[];
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
      };
    }>
  >(generateAPIPath('api/v2/farmingPlan/task-management-info/all'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getAllTaskManagerListWithoutAll = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ITaskManagerRes[];
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
      };
    }>
  >(generateAPIPath('api/v2/farmingPlan/task-management-info'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getTaskManageTracingList = async (params?: API.ListParamsReq) => {
  const res = await request<API.ResponseResult<ITaskManagerRes[]>>(
    generateAPIPath('api/v2/farmingPlan/task-management-info-tracing'),
    {
      method: 'GET',
      params: getParamsReqList(params),
    },
  );
  return {
    data: res.result,
  };
};

export type ICreateFarmingPlanTaskReq = {
  farming_plan_state: string;
  label: string;
  image?: any;
  start_date: string;
  end_date: string;
  assigned_to?: any;
  description?: any;
};
export interface ICreateFarmingPlanTaskRes {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  farming_plan_state: string;
  state_name: string;
  crop_name: string;
  label: string;
  image: any;
  supplies_id: any;
  supplies: any;
  start_date: string;
  end_date: string;
  index: any;
  assigned_to: any;
  involved_in: any;
  status: string;
  description: any;
  doctype: string;
}

export const createFarmingPlanTask = async (data: ICreateFarmingPlanTaskReq[]) => {
  let dataFormatted: undefined | ICreateFarmingPlanTaskReq[];
  try {
    if (data?.length) {
      // sắp xếp thứ tự các key trong obj
      const keys = Object.keys(data[0]);
      dataFormatted = data.map((item: any) => {
        const res: any = {};
        keys.forEach((key) => {
          res[key] = item[key];
        });
        return res;
      });
    } else {
      throw new Error();
    }
  } catch (error) {
    dataFormatted = data;
  }
  const res = await request<
    API.ResponseResult<{
      data: ICreateFarmingPlanTaskRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/task/array'), {
    method: 'POST',
    data: {
      tasks: dataFormatted,
    },
  });
  return res.result;
};
export type IUpdateFarmingPlanTaskReq = {
  name: string;
  farming_plan_state: string;
  label?: string;
  image?: any;
  start_date?: string;
  end_date?: string;
  assigned_to?: any;
  description?: any;
};
export interface IUpdateFarmingPlanTaskRes {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  farming_plan_state: string;
  state_name: string;
  crop_name: string;
  label: string;
  start_date: string;
  end_date: string;
  assigned_to: string;
  status: string;
  doctype: string;
}

export const updateFarmingPlanTask = async (data: IUpdateFarmingPlanTaskReq) => {
  const res = await request<
    API.ResponseResult<{
      data: IUpdateFarmingPlanTaskRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/task'), {
    method: 'PUT',
    data,
  });
  return res.result;
};
export const createFarmingPlanTaskAssignUser = async (data: {
  customer_user: string;
  task: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: {
        name: string;
        owner: string;
        creation: string;
        modified: string;
        modified_by: string;
        docstatus: number;
        idx: number;
        customer_user: string;
        task: string;
        doctype: string;
      };
    }>
  >(generateAPIPath('api/v2/farmingPlan/assignUser'), {
    method: 'POST',
    data,
  });
  return res.result;
};
export const createFarmingPlanDiaryTask = async (data: ICreateFarmingPlanTaskReq[]) => {
  let dataFormatted: undefined | ICreateFarmingPlanTaskReq[];
  try {
    if (data?.length) {
      // sắp xếp thứ tự các key trong obj
      const keys = Object.keys(data[0]);
      dataFormatted = data.map((item: any) => {
        const res: any = {};
        keys.forEach((key) => {
          res[key] = item[key];
        });
        return res;
      });
    } else {
      throw new Error();
    }
  } catch (error) {
    dataFormatted = data;
  }
  const res = await request<
    API.ResponseResult<{
      data: ICreateFarmingPlanTaskRes;
    }>
  >(generateAPIPath('api/v2/farmingPlan/task/array/diary'), {
    method: 'POST',
    data: {
      tasks: dataFormatted,
    },
  });
  return res.result;
};

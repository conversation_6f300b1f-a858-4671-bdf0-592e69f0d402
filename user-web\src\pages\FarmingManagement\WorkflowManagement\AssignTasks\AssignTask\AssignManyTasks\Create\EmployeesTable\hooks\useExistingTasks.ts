import { getTaskManagerList, getTaskManagerListV2 } from '@/services/farming-plan';
import { Task } from '@/services/vietplants/task';
import { useEffect, useState } from 'react';
import { DateRange, DepartmentState, EmployeeState } from '../types';
import { getFilterTasksForAssignment, transformExistingTaskToAssignment } from '../utils/filters';

export interface UseExistingTasksProps {
  dateRange: DateRange;
  departmentState: DepartmentState;
  employeeState: EmployeeState;
  selectedPlan: any;
  taskTemplates: Task[];
  yieldExpectedOutputs?: any;
}

export interface UseExistingTasksReturn {
  existingTasks: any[];
  loading: boolean;
  error: string | null;
  fetchExistingTasks: () => Promise<void>;
}

/**
 * Hook for fetching existing tasks based on filters
 * Uses the same API and filtering pattern as CheckingTable
 */
export const useExistingTasks = ({
  dateRange,
  departmentState,
  employeeState,
  selectedPlan,
  taskTemplates,
  yieldExpectedOutputs,
}: UseExistingTasksProps): UseExistingTasksReturn => {
  const [existingTasks, setExistingTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { dates } = dateRange;
  const { selectedDepartmentId } = departmentState;
  const { selectedEmployeeId } = employeeState;

  const fetchExistingTasks = async () => {
    // Only fetch if we have the required filters
    if (!dates || !selectedDepartmentId) {
      setExistingTasks([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('[useExistingTasks] Fetching existing tasks with filters:', {
        dates: dates.map((d) => d.format('YYYY-MM-DD')),
        departmentId: selectedDepartmentId,
        employeeId: selectedEmployeeId,
        selectedPlan: selectedPlan?.name,
      });

      // Extract environment_template_id values from yieldExpectedOutputs
      const environmentTemplateIds = yieldExpectedOutputs
        ? Object.values(yieldExpectedOutputs)
          .map((output: any) => output.environment_template_id)
          .filter(Boolean) // Remove any null/undefined values
        : [];

      console.log('[useExistingTasks] Extracted environment template IDs from yieldExpectedOutputs:', {
        yieldExpectedOutputs,
        environmentTemplateIds,
      });

      // Prepare filter parameters based on FarmingPlanTask entity structure
      // Note: Don't filter by assigned_to here if no specific employee is selected
      // This allows us to get all tasks for the department and then filter in the frontend
      const filterParams = getFilterTasksForAssignment({
        start_date: dates[0].format('YYYY-MM-DD'),
        end_date: dates[1].format('YYYY-MM-DD'),
        assigned_to: selectedEmployeeId || undefined, // Only filter by employee if one is specifically selected
        // department_id: selectedDepartmentId,
        // farming_plan_state: selectedPlan?.name || undefined,
        environment_id: environmentTemplateIds.length > 0 ? environmentTemplateIds : (selectedPlan?.environment_id || undefined),
        current: 1,
        pageSize: 1000,
        productionPlanId: selectedPlan?.name || '', // Filter by production plan ID
      });

      console.log('[useExistingTasks] Filter params being sent:', filterParams);

      // Use the same API as CheckingTable
      const response = await getTaskManagerListV2(filterParams);

      console.log('[useExistingTasks] API response:', response);

      if (response?.data) {
        console.log('[useExistingTasks] Raw API response data:', response.data);

        // Transform existing tasks to match our table structure
        const transformedTasks = response.data.map((task: any, index: number) =>
          transformExistingTaskToAssignment(task, index + 1, taskTemplates),
        );

        // For now, trust the API filtering - no additional frontend filtering needed
        const filteredTasks = transformedTasks;

        console.log('[useExistingTasks] Transformed existing tasks:', filteredTasks);
        console.log('[useExistingTasks] Employee filter was:', selectedEmployeeId);
        console.log('[useExistingTasks] Department filter was:', selectedDepartmentId);
        setExistingTasks(filteredTasks);
      } else {
        console.log('[useExistingTasks] No data in API response');
        setExistingTasks([]);
      }
    } catch (err: any) {
      console.error('[useExistingTasks] Error fetching existing tasks:', err);
      setError(err.message || 'Failed to fetch existing tasks');
      setExistingTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch when filters change
  useEffect(() => {
    fetchExistingTasks();
  }, [dates, selectedDepartmentId, selectedEmployeeId, selectedPlan?.name, yieldExpectedOutputs]);

  return {
    existingTasks,
    loading,
    error,
    fetchExistingTasks,
  };
};
